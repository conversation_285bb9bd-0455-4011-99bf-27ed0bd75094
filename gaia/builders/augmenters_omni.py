import os.path
import datetime

import django
django.setup()

from typing import Dict
from tld import get_tld
from langdetect import detect_langs
from agbase.models import Company
from agbase.sql import load_sql
from dealer.models import fetch_orgs_and_predictions
from gaia.babyspider.cached_babyfetch import CachedBabyFetch
from gaia.babyspider.babyspider_augmenters import PlainText
from gaia.babyspider.babyfetch import url_to_domain
from rawsql.sql import list_to_sql_string_list
from gaia.util.keyvalue import mproc_kv, keyvalue
from gaia.util.keyvalue.mproc_kv import KeyValueStore_jsongz_sharded_nomanifest
from gaia.caches import gaia_caches
from gaia.gaia_fs.paths import gfs_folder_local

import pandas as pd
#  ML dependencies
import learn.ml_org_dataset_flask_client
import learn.sbert.sbert_flask_client
import learn.simpletrans.xform_flask_client
import learn.simpletrans.xform_base
import learn.simpletrans.pred_dict_extractor

from gaia.gaia_ml.family.fam_hf.sbert import sbert_flask_client
from gaia.core.gaia_goid import parse_goid

from gaia.util.keyvector import keyvector
from gaia.util.formats.frame_incremental import NumpyArrayWriter

file_path = './gaia/builders/email_vendor_exclude_list.txt'
EMAIL_EXCLUDES = []
with open(file_path, 'r') as file:
    for domain in file:
        domain = domain.strip()
        EMAIL_EXCLUDES.append(domain)

print('loading EMAIL_EXCLUDES...')


WRITE_MISSING = True

#  use na_filter=False because the aplha-2 code for Namibia is NA.
GEO_REGIONS_DF = pd.read_csv('./ext_geo_country_region_list.csv', na_filter=False)

BABYSPIDER_CACHE_PATH = '/data/gaia/GAIA_FS/datasets/scraped/babyspider_main/'


def country_to_region(country_code=None):
    country_map = {'ROM': 'ROU', 'BAH': 'BHS', 'TAN': 'TZA'}

    if country_code in country_map.keys():
        country_code = country_map[country_code]

    if len(country_code) == 3:
        key = 'alpha-3'
    elif len(country_code) == 2:
        key = 'alpha-2'

    r = GEO_REGIONS_DF[GEO_REGIONS_DF[key] == country_code].to_dict(orient='records')[0]
    return {'continent': r['region'], 'sub_continent': r['sub-region'].replace(' ', ''), 'country_code': country_code}


def landetector(text=None):
    res = detect_langs(text)
    langs = {d.lang: d.prob for d in res}
    sorted_langs = sorted(langs.items(), key=lambda x: x[1], reverse=True)
    lang_data = {}
    lang_data['count'] = len(sorted_langs)

    # Get the first and second keys with the highest values
    if len(sorted_langs):
        lang_data['lang_1'] = sorted_langs[0][0]
        lang_data['lang_1_prob'] = sorted_langs[0][1]

    if len(sorted_langs) > 1:
        lang_data['lang_2'] = sorted_langs[1][0]
        lang_data['lang_2_prob'] = sorted_langs[1][1]

    if len(sorted_langs) > 2:
        lang_data['lang_2'] = sorted_langs[2][0]
        lang_data['lang_2_prob'] = sorted_langs[2][1]

    return lang_data


def parse_timestamp(timestamp_str):
    # Remove milliseconds and 'Z'
    timestamp_str = timestamp_str.replace('.000Z', '')
    # Parse the timestamp
    return datetime.strptime(timestamp_str, '%Y-%m-%dT%H:%M:%S')


def filter_dict_with_key(data_dict: Dict, dict_key: str) -> Dict:
    #  filter:
    #    dict_key absent
    #    dict_key is None
    return [goid for goid in data_dict.keys() if data_dict[goid].get(dict_key) is not None and data_dict[goid][dict_key] is not None]




    @property
    def formatted_name(self):
        return self.__class__.__name__.lower()

    @property
    def cache_path(self):
        try:
            return self.kv.file_path
        except AttributeError:
            return self.kv.folder_path

    def input_data_kvs_lookup(self, goid: str):
        res_list = []
        for kv in self.input_data_kvs:
            res = kv.get(goid)
            res_list.append(str(res))
        return '\n'.join(res_list)

    def close(self):
        self.kv.close()

    def compute(self):
        raise NotImplementedError

    def compute_with_cache(self, data_dict: Dict, refresh_cache: bool = False) -> Dict:
        cache_hit_dict = {}
        cache_miss_dict = {}

        print('AUGMENTER REFRESH CACHE? ... ', refresh_cache)

        #  get from cache
        for goid, dd in data_dict.items():
            #  if refresh_cache is True, skip cache
            if not refresh_cache and self.kv.get(goid):
                print('CACHE HIT', goid)
                cache_hit_dict[goid] = self.kv.get(goid)
            else:
                cache_miss_dict[goid] = dd
                print('CACHE MISS', goid)

        print(f'{len(cache_hit_dict.keys())} cache hits.')
        print(f'{len(cache_miss_dict.keys())} cache misses')

        #  compute
        res_dict = self.compute(data_dict=cache_miss_dict)

        #  set cache
        for goid, dd in res_dict.items():
            #print('SETTING CACHE...')
            self.kv.set(goid, dd)

        augmented_dict = {**cache_hit_dict, **res_dict}

        if len(augmented_dict.keys()) != len(data_dict.keys()):
            print('DEBUG Augmenter Error: augmented length mismatch.....')
            #import pdb; pdb.set_trace()

        #  return full results dict
        #  should have equal number of keys as input data dict
        return {**cache_hit_dict, **res_dict}


    def parse_goids(self, goid_list=None, filter_src='cbuuid'):
        uuid_dict = {}
        print('parse these goids...', list(goid_list)[0])
        #uuid_list = [parse_goid(goid)['srckey'] for goid in goid_list if parse_goid(goid)['src'] == filter_src]
        for goid in goid_list:
            uuid = self.goid_to_uuid(goid)
            if uuid:
                uuid_dict[uuid] = goid
        return uuid_dict

    @staticmethod
    def goid_to_uuid(goid, filter_src='cbuuid'):
        if parse_goid(goid)['src'] == filter_src:
            return parse_goid(goid)['srckey']
        #raise ValueError('cannot convert goid. filter_src mismatch.')
        return None




class Agspider(Augmenter):

    @staticmethod
    def compute(data_dict=None):
        """
        Gets a batch of docs from agspider, with exactly these ids.
        Returns a dict
        """

        print("fn: get_agspider...")
        uuids_doc_dict = data_dict['uuids_doc_dict']
        uuid_list = list(uuids_doc_dict.keys())

        ES_ALIAS = 'local'
        es_conn = es_connect(alias=ES_ALIAS)
        src_idx = 'idx_agspider'

        if not es_conn:
            raise ValueError('Elasticsearch connection argument required! es_conn: {}'.format(es_conn))

        agspider_dict = {}

        chunkize_version = False
        if chunkize_version:
            #  holds ids
            chunks = __chunkize(uuid_list, max_chunk_size=10)

            for chunk in chunks:
                try:
                    print('getting chunk...' + str(len(chunk)))
                    agspider_res, _ = es_conn.docs_mget_fast(
                        ids=chunk, index=src_idx)
                    res = agspider_res['hits']['hits']
                    print('got ' + str(len(res)))

                    for rec in res:
                        uuid = rec["_id"]
                        agspider_dict[uuid] = rec['_source']['docs']  # ?

                except Exception as e:
                    msg = str(e)
        else:
            agspider_res, query = es_conn.docs_mget(
                ids=uuid_list,
                index=src_idx,
                max_size=2000
            )
            for rec in agspider_res['hits']['hits']:
                uuid = rec["_id"]
                agspider_dict[uuid] = rec['_source']['docs']  # ?
        #  returns a dict
        return agspider_dict


class Agbase(Augmenter):

    def compute(self, goid_dict=None):
        """
        Gets a batch of docs from cb company, with exactly these ids.
        Returns a dict.

        Requires Django
        """

        res_dict = {}

        goid_list = list(goid_dict.keys())
        uuid_dict = self.parse_goids(goid_list, filter_src='cbuuid')

        if not len(uuid_dict.keys()):
             return res_dict

        keys_to_remove = ['rec_search_vector', 'rec_search_text', 'ml_data']

        qs = Company.objects.filter(cb_uuid__in=uuid_dict.keys()).values()

        for ac in qs:
            uuid = ac['cb_uuid']
            goid = uuid_dict[uuid]
            for key in keys_to_remove:
                if ac.get(key):
                    del ac[key]
            res_dict[goid] = ac

        return res_dict


class BabyspiderPlaintext(Augmenter):

    @staticmethod
    def compute(data_dict=None):
        """
        Gets a batch of docs from babyspider, with exactly these ids.
        Returns a dict
        """
        print("fn: get_babyspider_plain_text...")
        #uuids_homepage_dict = data_dict['uuids_homepage_dict']
        res_dict = {}

        plaintext = PlainText()
        for goid, dd in data_dict.items():
            url = dd.get('homepage_url')

            if not url:
                continue

            domain = url_to_domain(url=url)

            if not domain:
                continue

            print(f'get domain: { domain }')
            #  get raw record from babyspider json cache
            src_folder = gaia_caches.kv_stores['bs.babyspider_maincache_folder_fsmp']['path']
            src_kv = mproc_kv.KeyValueStore_jsongz_sharded(folder_path=BABYSPIDER_CACHE_PATH, shards=1024)
            try:
                data = src_kv.get(domain)
            except OSError as e:
                print('.......KV READ FILE ERROR: ', domain)
                continue
            except Exception as e:
                print('generic exception...', [domain, e])
                continue

            if not data:
                continue

            try:
                dd = data['https']
            except KeyError:
                dd = data['http']

            try:
                html = dd.get('content', {})['html']
            except KeyError:
                # print('no html key found!', url)
                continue

            try:
                res = plaintext.parse(html=html)
            except OverflowError as e:
                print(f'OverflowError: {url}, error: {e}')
                continue

            res_dict[goid] = res

        return res_dict


class Babyspider(Augmenter):

    @staticmethod
    def compute(data_dict=None):
        """
        Gets a batch of docs from babyspider, with exactly these ids.
        Returns a dict
        """
        print("fn: get_babyspider...")

        uuids_homepage_dict = data_dict['uuids_homepage_dict']
        baby_dict = {}
        babyfetch = CachedBabyFetch()

        for uuid in uuids_homepage_dict.keys():
            url = uuids_homepage_dict[uuid]
            if url:
                #  TODO: get from keyvalue store
                #baby_data = babystore_load(url)
                #if baby_data:
                #    print(f'hit')
                #babystore = BabyStore()
                #baby_data = babystore.load(url)
                # this is a one-off to populate cache
                #  TODO: get frpom cache - does that return key, value?
                #  TODO: format should mach load(url)

                #  goal:

                cached_babyfetch = CachedBabyFetch()
                baby_data = cached_babyfetch.get_cache(url=url)
                baby_data = babyfetch.get_cache(url=url)

                #babystore = BabyStore()
                #baby_data = babystore.load_file(url=url)
            else:
                print('babyspider: no url!')
                baby_data = None
            baby_dict[uuid] = baby_data
        #  returns a dict
        return baby_dict


class Dealer(Augmenter):

    def compute(self, goid_dict=None):
        """
        Gets a batch of docs from dealer, with exactly these ids.
        Returns a dict
        """

        res_dict = {}
        goid_list = list(goid_dict.keys())
        uuid_dict = self.parse_goids(goid_list, filter_src='cbuuid')

        if not len(uuid_dict.keys()):
             print('.............................................No cbuuids found.')
             return res_dict

        try:
            ops = fetch_orgs_and_predictions(
                uuid_list=uuid_dict.keys(),
                has_prediction_already=True,
                min_prob=None,
                include_old=True,
                recency_weeks=None,
                ratings_filter='off',
                sort_list=["uuid"],
                limit=len(uuid_dict.keys())
            )
        except Exception as e:
            print('get_dealer. error: {}'.format(e))
            raise e

        copy_fields = (
            'incl_agrifood_tech',
            'incl_agrifood',
            'edu_rank_times1',
            'edu_rank_empirical1',
            'edu_rank_nature1',
            'max_inv_comp_count',
            'max_inv_acq_count',
            'max_inv_ipo_count',
            'max_hitrate_acq',
            'max_hitrate_ipo',
            'max_inv_round_median',
            'max_inv_round_sum',
            'degree_phd_count',
            'ppl_count',
            #  edu geo
            'edu_rank_geo_country_empirical1',
            'edu_rank_geo_subregion_empirical1',
            'edu_rank_geo_region_empirical1',
            #  degree
            'degree_grad_count',
            'degree_grad_any',
            'degree_master_any',
            'degree_master_count',
            'degree_phd_any',
            'degree_phd_count',
            'pastjobs_funding',
            'score_early',
            #  score early
            'score_early_founded',
            'score_early_empl',
            'score_early_raised_rounds',
            'score_early_raised_amt',
            #  elite
            'sum_pastjob_org_elite_rating',
            'sum_pastjob_org_elite_migr_to_gt5',
            'sum_pastjob_org_elite_migr_to_gt1',
            'sum_pastjob_org_elite_migr_to_gt0',
            #  Pastjob E2 Ranks
            'min_pastjob_org_e2_rank_fwd',
            'min_pastjob_org_e2_rank_rev',
            'min_pastjob_org_e2_rank_undir',
            #  Geo
            'geo_continent',
            'geo_sub_continent'
        )

        for row in ops:
            uuid = row['uuid']
            goid = uuid_dict[row['uuid']]
            res_dict[goid] = {cf: row[cf] for cf in copy_fields}

        return res_dict


class People(Augmenter):

    def compute(self, goid_dict=None):
        """
        Gets a batch of docs from cb16x_orgspeople_summary, with exactly these ids.
        Returns a dict
        """

        res_dict = {}

        goid_list = list(goid_dict.keys())
        uuid_dict = self.parse_goids(goid_list, filter_src='cbuuid')

        if not len(uuid_dict.keys()):
             return res_dict

        company_uuid_string_list = list_to_sql_string_list(uuid_dict.keys())
        ppl = None
        # cb16x_orgspeople_summary_aug
        try:
            ppl = load_sql(
                "select * from cb16x_orgspeople_summary_aug where company_uuid in ( %s )" % company_uuid_string_list)
        except Exception as e:
            print('Exception: fn: get_people. error: {}'.format(e))

        if not ppl:
            print('ppl query failed to retrieve any results.')

        copy_fields = ['person_uuid', 'first_name', 'last_name', 'title', 'job_type', 'job_uuid', 'pastjobs',
                       'company_name', 'schools', 'edu_rank', ]
        for row in ppl:
            uuid = row['company_uuid']
            goid = uuid_dict[row['company_uuid']]
            single_rec = {cf: row[cf] for cf in copy_fields}
            if uuid in res_dict:
                res_dict[goid].append(single_rec)
            else:
                res_dict[goid] = [single_rec, ]
        #  returns a dict
        return res_dict


class Investor(Augmenter):

    def compute(self, goid_dict=None):
        """
        Gets a batch of docs from cb_fr_invstr_invstmt, with exactly these ids.
        Returns a dict
        """

        res_dict = {}
        goid_list = list(goid_dict.keys())
        uuid_dict = self.parse_goids(goid_list, filter_src='cbuuid')

        if not len(uuid_dict.keys()):
             return res_dict

        company_uuid_string_list = list_to_sql_string_list(uuid_dict.keys())

        sql = """
                select *
            from cb_fr_invstr_invstmt ii1
            left join agbase_views_cbinv_score1 sc1 on ii1.inv_uuid = sc1.invstr_uuid
            where org_uuid in ( %s )
            """ % company_uuid_string_list

        #  execute sql
        inv = load_sql(sql)

        copy_fields = ['domain', 'investor_type', 'investor_permalink', 'investor_type', 'investor_name', 'loc',
                       'inv_acq_count', 'inv_ipo_count', 'inv_comp_count', 'hitrate_acq', 'hitrate_ipo',
                       'funding_round_type', 'fr_uuid', 'raised_amount_usd', 'announced_on', 'inv_round_median',
                       'inv_round_sum', 'is_lead_investor', 'inv_uuid']
        for row in inv:
            uuid = row['org_uuid']
            goid = uuid_dict[row['org_uuid']]
            single_rec = {cf: row[cf] for cf in copy_fields}
            if goid in res_dict:
                res_dict[goid].append(single_rec)
            else:
                res_dict[goid] = [single_rec, ]
        #  returns a dict
        return res_dict


class Rounds(Augmenter):

    def compute(self, goid_dict=None):
        """
        Gets a batch of docs from ccb16_funding_rounds, with exactly these ids.
        Returns a dict
        """

        res_dict = {}
        goid_list = list(goid_dict.keys())
        uuid_dict = self.parse_goids(goid_list, filter_src='cbuuid')

        if not len(uuid_dict.keys()):
             return res_dict

        company_uuid_string_list = list_to_sql_string_list(uuid_dict.keys())

        rqq = """
                select *
                from cb_fr_all left

                join
                cb16_funding_rounds
                on
                fr_uuid = funding_round_uuid
                where company_uuid in ( %s )
                """ % company_uuid_string_list

        #  load sql
        rounds = load_sql(rqq)

        copy_fields = ['raised_amount_currency_code', 'raised_amount_usd', 'investment_type',
                       'post_money_valuation_usd', 'announced_on', 'funding_round_type', 'funding_round_uuid']
        for row in rounds:
            uuid = row['org_uuid']
            goid = uuid_dict[row['org_uuid']]
            single_rec = {cf: row[cf] for cf in copy_fields}
            if uuid in res_dict:
                res_dict[goid].append(single_rec)
            else:
                res_dict[goid] = [single_rec, ]
        #  returns a dict

        return res_dict


class Knn(Augmenter):

    @staticmethod
    def compute(data_dict=None, dict_key='concat_description'):
        print('fn: knn...')

        #  reject where dict_key is None
        goid_sort_list = filter_dict_with_key(data_dict, dict_key)
        goid_sort_list.sort()
        goid_doc_list_sort = [data_dict[goid][dict_key] for goid in goid_sort_list]

        res_dict = {}

        print("Querying learn.sbert.sbert_flask_client...")
        preds = learn.sbert.sbert_flask_client.embeds(goid_doc_list_sort)

        for i, goid in enumerate(goid_sort_list):
            res_dict[goid] = preds['embed'][i]  # [:2]
        return res_dict


class SBert_EMB02xlm(Augmenter):

    #np_writer = NumpyArrayWriter(folder='/var/lib/gaia/GAIA_FS/omnibuilder_pipeline/run_0000/001_base_ob_out/sbert_vector/')

    dict_key = 'concat_description'

    #@staticmethod
    def compute(self, data_dict=None):
        print('augmenter: sbert emb02xlm...')
        print(f'SBert_EMB02xlm data_dict len: {len(data_dict.keys())}')

        sbert_model_key = 'EMB_02_xlm'

        #  reject where dict_key is None
        goid_sort_list = filter_dict_with_key(data_dict, self.dict_key)
        goid_sort_list.sort()
        goid_doc_list_sort = [data_dict[goid][self.dict_key] for goid in goid_sort_list]

        res_dict = {}

        print(f'Querying sbert_flask_client. Model: { sbert_model_key }')
        preds = sbert_flask_client.embeds(
            texts=goid_doc_list_sort, sbert_model_key=sbert_model_key)

        for i, goid in enumerate(goid_sort_list):
            res_dict[goid] = preds['embed'][i]
            #  set vector store
            #print('setting vector store...')
            #np.array(preds['embed'][i])
            #self.vector_kv.accept(preds['embed'][i])
        return res_dict


class SBert_EMB02xlm_Describe(SBert_EMB02xlm):
    """Calls Sbert predict with EMB_02_xlm model. Uses existing cache as input."""

    kv_paths = ['/var/lib/gaia/GAIA_FS/omnibuilder_pipeline/kv_cache/augmenterclerkfetchandparsemulti__describe_parse__basic__claude3haiku20240307.db']

    def __init__(self):
        super().__init__()
        self.input_data_kvs = [keyvalue.KeyValueStore_sqlite3(file_path=kv_path) for kv_path in self.kv_paths]

    def compute(self, data_dict=None):
        sbert_model_key = 'EMB_02_xlm'
        goid_sort_list = [goid for goid in data_dict.keys()]
        goid_sort_list.sort()

        input_texts_sorted = []

        for goid in goid_sort_list:
            res = self.input_data_kvs_lookup(goid)
            input_texts_sorted.append(res)

        preds = sbert_flask_client.embeds(
            texts=input_texts_sorted, sbert_model_key=sbert_model_key)

        res_dict = {}
        for i, goid in enumerate(goid_sort_list):
            res_dict[goid] = preds['embed'][i]

        return res_dict


class Mlorg(Augmenter):

    @staticmethod
    def compute(data_dict=None, dict_key='concat_description'):
        print("augmenter: get_mlorg... ")

        #  reject where dict_key is None
        goid_sort_list = filter_dict_with_key(data_dict, dict_key)
        goid_sort_list.sort()
        goid_doc_list_sort = [data_dict[goid][dict_key] for goid in goid_sort_list]

        preds = learn.ml_org_dataset_flask_client.get_org_text_predictions_multi(goid_doc_list_sort)

        res_dict = {}
        for i, goid in enumerate(goid_sort_list):
            pred = preds[i]
            del pred['cat_likely']
            del pred['tag_likely']
            res_dict[goid] = pred
        return res_dict


class Xform(Augmenter):

    @staticmethod
    def compute(data_dict=None, dict_key='concat_description'):
        print("augmenter: get_xform_preds... ")

        #  reject where dict_key is None
        goid_sort_list = filter_dict_with_key(data_dict, dict_key)
        goid_sort_list.sort()
        goid_doc_list_sort = [data_dict[goid][dict_key] for goid in goid_sort_list]

        filtered_dict = {}

        #  only if dict_key exists
        for goid, dd in data_dict.items():
            if dd.get(dict_key):
                filtered_dict[goid] = dd

        res_dict = {}

        if len(goid_doc_list_sort):

            try:
                res = learn.simpletrans.xform_flask_client.predictions(goid_doc_list_sort)
            except Exception as e:
                print('aug error:', e)
                raise

            for i, goid in enumerate(goid_sort_list):
                #print(f'res.probs. type: { type(res["probs"]) }, len: { len(res["probs"]) }')
                try:
                    res_dict[goid] = learn.simpletrans.pred_dict_extractor.pred_dict_extract_recur(
                       res["probs"], index=i)
                except Exception as e:
                    print('Augmenter ERROR: ', e)
                    #import pdb; pdb.set_trace()
        return res_dict



class LangDetectWeb(Augmenter):

    def compute(self, goid_dict=None):

        goid_list = list(goid_dict.keys())

        res_dict = {}

        #  get plaintext pages from KV
        cache_path = gaia_caches.gfs_folder_local(tree='gfs_datasets', sub_folder='')
        src_kv = keyvalue.KeyValueStore_sqlite3(file_path=cache_path + '/baby2plaintext.db')

        uuid_dict = self.parse_goids(goid_dict.keys(), filter_src='cbuuid')
        uuid_list = list(uuid_dict.keys())
        pages = src_kv.get_mult(uuid_list)

        if not pages or len(pages) < 1:
            print(f'pages are empty!')
            return res_dict

        print(f'{len(pages.keys())} pages found')
        for uuid, plaintext in pages.items():
            if not plaintext:
                continue
            #print('plaintext: \n', plaintext[:50], '\n')
            #  remove linebreaks
            cleantext = plaintext.replace('\n', '').replace('\r', '')

            try:
                lang_data = landetector(text=cleantext)
            except Exception as e:
                #print('langdetectsummary error: ', e, cleantext)
                #print('skipping this cleantext....' , cleantext)
                continue


            goid = uuid_dict[uuid]
            res_dict[goid] = lang_data
        return res_dict


class LangDetectSummary(Augmenter):

    @staticmethod
    def compute(data_dict=None, dict_key='concat_description'):
        print('augmenter: langdetectsummary...')

        res_dict = {}
        for goid, dd in data_dict.items():
            summary_text = dd['concat_description']

            if not summary_text:
                continue

            try:
                lang_data = landetector(text=summary_text)
            except Exception as e:
                #print('langdetectsummary error: ', e, summary_text)
                #print('skipping this cleantext....' , cleantext)
                continue

            res_dict[goid] = lang_data
        return res_dict


class SubscribersByDomain(Augmenter):

    @staticmethod
    def compute(data_dict=None):
        """
        Gets a batch from subscribers cache, with domain as key.
        Returns a dict
        """
        print("augmenter: subscriber by domain...")
        uuids_homepage_dict = data_dict['uuids_homepage_dict']
        res_dict = {}

        for uuid in uuids_homepage_dict.keys():
            url = uuids_homepage_dict[uuid]

            #  exclude None and explicit excludes
            if not url or url in EMAIL_EXCLUDES:
                continue

            try:
                tld_obj = get_tld(url, as_object=True)
            except Exception as e:
                print(e)
                continue

            domain = tld_obj.fld
            #print('find domain', domain)

            #  get kv instance
            CACHE_PATH = gaia_caches.gfs_folder_local(tree='gfs_datasets')
            kv_path = os.path.join(CACHE_PATH, 'subscribers.db')
            kv = keyvalue.KeyValueStore_sqlite3(file_path=kv_path)

            try:
                res = kv.get(domain)
            except OSError as e:
                print('.......KV READ FILE ERROR: ', domain)
                continue
            except Exception as e:
                print('generic exception...', [domain, e])
                continue

            if not res:
                continue
            #print('found match', domain, len(res))

            #  get most recent
            timestamps = [parse_timestamp(item['created_at']) for item in res]
            i = timestamps.index(max(timestamps))
            most_recent_created_at = res[i]['created_at']
            most_recent_email = res[i]['email_address']

            res_dict[uuid] = {'list': res, 'count': len(res), 'most_recent_created_at': most_recent_created_at, 'most_recent_email': most_recent_email}

        return res_dict


class GeoRegion(Augmenter):

    @staticmethod
    def compute(data_dict=None, dict_key='source'):
        print('augmenter: geo region...')

        res_dict = {}

        for goid, record in data_dict.items():
            res = {}
            try:
                country_code = record['source']['country_code']
            except KeyError:
                continue

            if country_code is None:
                continue

            #print(f"{ goid }: { record['source']['country_code'] } => { country_code }")

            try:
                res_dict[goid] = country_to_region(country_code=country_code)
            except Exception as e:
                print('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++ geo error', e, record)
                import pdb; pdb.set_trace()
                continue

        return res_dict

