import streamlit as st
import pandas as pd
import numpy as np
import json
import sys
sys.path.append("../../..")

from gaia.elasticsearch.gaia_elasticsearch import GaiaElastico
from search_utils import connect_opensearch, filter_records_omni


DEFUALT_CLUSTER_ALIAS = 'opensearch_worker_giant'

# Set page config
st.set_page_config(
    page_title="Gaia Advanced Search | Opensearch Explorer",
    page_icon="🔍",
    layout="wide"
)

# Add custom CSS
def load_css():
    st.markdown("""
    <style>
    .search-result {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 10px;
        background-color: #f9f9f9;
    }
    .search-result:hover {
        background-color: #f0f0f0;
        border-color: #aaa;
    }
    .company-name {
        font-size: 1.5em;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    .goid {
        font-size: 0.8em;
        color: #7f8c8d;
        margin-bottom: 10px;
    }
    .field-name {
        font-weight: bold;
        color: #555;
    }
    .numeric-value {
        color: #2c7fb8;
        font-weight: bold;
    }
    .text-value {
        color: #333;
    }
    .metadata {
        font-size: 0.8em;
        color: #777;
        margin-top: 5px;
    }
    </style>
    """, unsafe_allow_html=True)

load_css()

# Initialize session state
if 'records' not in st.session_state:
    st.session_state.records = None
if 'query' not in st.session_state:
    st.session_state.query = None
if 'selected_index' not in st.session_state:
    st.session_state.selected_index = None
if 'text_fields' not in st.session_state:
    st.session_state.text_fields = []
if 'numeric_fields' not in st.session_state:
    st.session_state.numeric_fields = []
if 'all_fields' not in st.session_state:
    st.session_state.all_fields = []

# Function to get all indices from OpenSearch
@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_indices():
    try:
        # Connect to OpenSearch
        elastico = GaiaElastico()
        elastico.connect(alias=DEFUALT_CLUSTER_ALIAS)

        # Get all indices
        indices_info = elastico.es.cat.indices(format="json")
        indices = [index['index'] for index in indices_info if not index['index'].startswith('.')]
        return indices
    except Exception as e:
        st.error(f"Error connecting to OpenSearch: {str(e)}")
        return []

# Function to get field mappings for an index
@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_field_mappings(index_name):
    try:
        # Connect to OpenSearch
        elastico = GaiaElastico()
        elastico.connect(alias=DEFUALT_CLUSTER_ALIAS)

        # Get mappings for the index
        mappings = elastico.es.indices.get_mapping(index=index_name)

        # Extract field names and types
        properties = mappings[index_name]['mappings'].get('properties', {})

        text_fields = []
        numeric_fields = []
        all_fields = []

        def process_properties(props, prefix=""):
            for field_name, field_info in props.items():
                full_field_name = f"{prefix}{field_name}"
                all_fields.append(full_field_name)

                if 'properties' in field_info:
                    # Nested field, recurse
                    process_properties(field_info['properties'], f"{full_field_name}.")
                else:
                    field_type = field_info.get('type')
                    if field_type in ['text', 'keyword']:
                        text_fields.append(full_field_name)
                    elif field_type in ['long', 'integer', 'short', 'byte', 'double', 'float', 'half_float', 'scaled_float']:
                        numeric_fields.append(full_field_name)

        process_properties(properties)

        return text_fields, numeric_fields, all_fields
    except Exception as e:
        st.error(f"Error getting field mappings: {str(e)}")
        return [], [], []

# Function to search OpenSearch
def search_opensearch(index, text_query, text_field, numeric_field, numeric_range, limit=100):
    try:
        # Connect to OpenSearch
        elastico = GaiaElastico()
        elastico.connect(alias=DEFUALT_CLUSTER_ALIAS)

        # Build query
        query_parts = []

        # Add text query if provided
        if text_query and text_field:
            query_parts.append({
                "match": {
                    text_field: {
                        "query": text_query,
                        "operator": "and"
                    }
                }
            })

        # Add numeric range query if provided
        if numeric_field and numeric_range:
            query_parts.append({
                "range": {
                    numeric_field: {
                        "gte": numeric_range[0],
                        "lte": numeric_range[1]
                    }
                }
            })

        # Combine queries
        if query_parts:
            query_body = {
                "query": {
                    "bool": {
                        "must": query_parts
                    }
                },
                "size": limit
            }
        else:
            # If no specific query, return all documents (up to limit)
            query_body = {
                "query": {
                    "match_all": {}
                },
                "size": limit
            }

        # Execute search
        res = elastico.es.search(index=index, body=query_body)

        # Process results
        records = []
        for hit in res['hits']['hits']:
            record = hit['_source']
            record['_id'] = hit['_id']
            record['_score'] = hit['_score']
            records.append(record)

        return records, query_body
    except Exception as e:
        st.error(f"Error searching OpenSearch: {str(e)}")
        return [], {}

# Main app
st.title("🔍 Gaia Advanced Search | Opensearch Explorer")
st.write("Search and explore data in your OpenSearch indices")

# Get all indices
indices = get_indices()

# Sidebar for search controls
with st.sidebar:
    st.header("Search Controls")

    # Index selection
    selected_index = st.selectbox(
        "Select Index",
        options=indices,
        index=0 if indices else None,
        key="index_selector"
    )

    if selected_index and selected_index != st.session_state.selected_index:
        # Update field mappings when index changes
        text_fields, numeric_fields, all_fields = get_field_mappings(selected_index)
        st.session_state.text_fields = text_fields
        st.session_state.numeric_fields = numeric_fields
        st.session_state.all_fields = all_fields
        st.session_state.selected_index = selected_index

    # Text search
    st.subheader("Text Search")
    text_field = st.selectbox(
        "Select Text Field",
        options=st.session_state.text_fields,
        index=0 if st.session_state.text_fields else None,
        key="text_field_selector"
    )

    text_query = st.text_input(
        "Search Text",
        key="text_query_input"
    )

    # Numeric filter
    st.subheader("Numeric Filter")
    numeric_field = st.selectbox(
        "Select Numeric Field",
        options=st.session_state.numeric_fields,
        index=0 if st.session_state.numeric_fields else None,
        key="numeric_field_selector"
    )

    numeric_range = None
    if numeric_field:
        # Get min/max values for the selected numeric field
        try:
            elastico = GaiaElastico()
            elastico.connect(alias='opensearch_worker_giant')

            stats_query = {
                "aggs": {
                    "field_stats": {
                        "stats": {
                            "field": numeric_field
                        }
                    }
                },
                "size": 0
            }

            stats_res = elastico.es.search(index=selected_index, body=stats_query)
            field_stats = stats_res['aggregations']['field_stats']

            min_val = field_stats['min']
            max_val = field_stats['max']

            # Handle edge cases
            if min_val == max_val:
                min_val = min_val - 0.1
                max_val = max_val + 0.1

            numeric_range = st.slider(
                f"Range for {numeric_field}",
                min_value=float(min_val),
                max_value=float(max_val),
                value=(float(min_val), float(max_val)),
                key="numeric_range_slider"
            )
        except Exception as e:
            st.warning(f"Could not get range for field {numeric_field}: {str(e)}")

    # Search button
    search_button = st.button("Search", key="search_button")

    # Limit results
    limit = st.number_input("Max Results", min_value=1, max_value=1000, value=100, key="limit_input")

# Main content area
if search_button and selected_index:
    with st.spinner("Searching..."):
        records, query = search_opensearch(
            selected_index,
            text_query,
            text_field,
            numeric_field,
            numeric_range,
            limit
        )
        st.session_state.records = records
        st.session_state.query = query

# Display results
if st.session_state.records is not None:
    st.header(f"Search Results ({len(st.session_state.records)} records)")

    # Show query
    with st.expander("View Query"):
        st.json(st.session_state.query)

    # Display results
    if len(st.session_state.records) > 0:
        # Create tabs for different views
        tab1, tab2 = st.tabs(["Card View", "Table View"])

        with tab1:
            # Card view
            for record in st.session_state.records:
                with st.container():
                    st.markdown(f"""
                    <div class="search-result">
                        <h3>{record.get('company_name', record.get('_id', 'Unnamed Record'))}</h3>
                        <p><span class="field-name">ID:</span> <span class="text-value">{record.get('_id', 'N/A')}</span></p>
                        <p><span class="field-name">Score:</span> <span class="numeric-value">{record.get('_score', 'N/A')}</span></p>
                    """, unsafe_allow_html=True)

                    # Display selected fields
                    if text_field and text_field in record:
                        st.markdown(f"""
                        <p><span class="field-name">{text_field}:</span> <span class="text-value">{record.get(text_field, 'N/A')}</span></p>
                        """, unsafe_allow_html=True)

                    if numeric_field and numeric_field in record:
                        st.markdown(f"""
                        <p><span class="field-name">{numeric_field}:</span> <span class="numeric-value">{record.get(numeric_field, 'N/A')}</span></p>
                        """, unsafe_allow_html=True)

                    # Show all fields in an expander
                    with st.expander("View All Fields"):
                        # Convert record to DataFrame for better display
                        record_df = pd.json_normalize(record)
                        st.dataframe(record_df)

                    st.markdown("</div>", unsafe_allow_html=True)

        with tab2:
            # Table view
            # Convert records to DataFrame
            records_df = pd.DataFrame(st.session_state.records)

            # Select columns to display
            display_columns = ['_id', '_score']
            if text_field and text_field in records_df.columns:
                display_columns.append(text_field)
            if numeric_field and numeric_field in records_df.columns:
                display_columns.append(numeric_field)

            # Add other important columns if they exist
            for col in ['name', 'description', 'GOID', 'uuid']:
                if col in records_df.columns and col not in display_columns:
                    display_columns.append(col)

            # Display DataFrame
            st.dataframe(
                records_df[display_columns] if all(col in records_df.columns for col in display_columns) else records_df,
                use_container_width=True
            )
    else:
        st.info("No records found matching your search criteria.")
else:
    st.info("Select an index and search parameters, then click 'Search' to see results.")

# Footer
st.markdown("---")
st.markdown("Gaia Advanced Search | Opensearch Explorer | Gaia Tools")
